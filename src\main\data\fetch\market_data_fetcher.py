import datetime
import os
import shutil
from abc import ABC, abstractmethod
from enum import Enum
from pathlib import Path
from typing import Dict, Optional

import pandas as pd
from config.config_loader import ConfigLoader
from histdata import download_hist_data as dl
from histdata.api import Platform as P
from histdata.api import TimeFrame as TF
from pandas_datareader import data as web


class DataType(Enum):
    """市場データの種類を定義する列挙型"""

    FOREX = "forex"  # 為替データ
    INTEREST_RATE = "interest_rate"  # 金利データ
    ECONOMIC_INDICATOR = "economic_indicator"  # 経済指標データ
    LABOR_INDEX = "labor_index"  # 労働指標データ


class DataSource(Enum):
    """データソースを定義する列挙型"""

    FRED = "fred"  # FRED（Federal Reserve Economic Data）
    HISTDATA = "histdata"


class MarketDataFetcher:
    """様々なソースから市場データを取得するためのクラス。設定ファイルから取得設定を読み込む。"""

    def __init__(self, config_path: str = None):
        """
        データ取得クラスの初期化
        Args:
            config_path (str, optional): 設定ファイルのパス
        """
        self.config = ConfigLoader(config_path)
        self._initialize_data_sources()

    def _initialize_data_sources(self):
        """各データソースの初期化"""
        self.sources = {
            DataSource.FRED.value: PandasDatareaderDataSource(self.config),
            DataSource.HISTDATA.value: HistadaDataSource(self.config),
        }

    def fetch_data(
        self,
        data_type: DataType,
        symbol: str,
        start_date: str,
        end_date: str,
        source: DataSource = None,
        **kwargs,
    ) -> Optional[pd.DataFrame]:
        """
        指定したデータタイプとソースからデータを取得
        """
        if source is None:
            source = self._get_default_source(data_type)

        return self.sources[source.value].fetch_data(
            data_type=data_type,
            symbol=symbol,
            start_date=start_date,
            end_date=end_date,
            **kwargs,
        )

    def _get_default_source(self, data_type: DataType) -> DataSource:
        """設定ファイルからデフォルトのデータソースを取得"""
        source_name = self.config.get_default_source(data_type.value)
        return DataSource[source_name.upper()]

    def fetch_all_data(
        self, data_type: DataType, period_type: str = "default"
    ) -> Dict[str, pd.DataFrame]:
        """指定したデータタイプの全シンボルのデータを取得"""
        symbols = self.config.get_symbols(data_type.value)
        period = self.config.get_time_period(period_type)

        results = {}
        for symbol in symbols:
            df = self.fetch_data(
                data_type=data_type,
                symbol=symbol,
                start_date=period.get("start_date"),
                end_date=period.get("end_date"),
            )
            if df is not None:
                results[symbol] = df
                self._save_data(df, data_type, symbol)

        return results

    def _save_data(self, df: pd.DataFrame, data_type: DataType, symbol: str):
        """APIから取得した生データをdata/raw配下に保存"""
        storage_path = self.config.get_storage_path(f"raw/{data_type.value}")
        storage_path.mkdir(parents=True, exist_ok=True)

        file_path = storage_path / f"{symbol.replace('/', '_')}.parquet"
        df.to_parquet(file_path)


class BaseDataSource(ABC):
    """データソースの基底クラス"""

    @abstractmethod
    def fetch_data(
        self, data_type: DataType, symbol: str, start_date: str, end_date: str, **kwargs
    ) -> Optional[pd.DataFrame]:
        """データを取得する抽象メソッド"""
        pass


class PandasDatareaderDataSource(BaseDataSource):
    """Pandas Datareaderを使用してデータを取得するデータソース"""

    def __init__(self, config: ConfigLoader):
        self.config = config

    def fetch_data(
        self, data_type: DataType, symbol: str, start_date: str, end_date: str, **kwargs
    ) -> Optional[pd.DataFrame]:
        """Pandas Datareaderを使用してデータを取得する"""
        try:
            if data_type == DataType.FOREX:
                default_source = self.config.get_default_source(data_type.value)
                df = web.DataReader(symbol, default_source, start_date, end_date)
                return df
            else:
                raise ValueError(f"Unsupported data type: {data_type}")
        except Exception as e:
            print(f"Pandas Datareader error: {e}")
            return None


class HistadaDataSource(BaseDataSource):
    """histdataを使用してデータを取得するデータソース"""

    def __init__(self, config: ConfigLoader):
        self.config = config

    def _download_data(self, symbol: str, year: int, month: Optional[int] = None):
        """histdataからデータをダウンロードする"""
        try:
            dl(
                year=str(year),
                month=str(month) if month is not None else None,
                pair=symbol.lower().replace("/", ""),
                platform=P.GENERIC_ASCII,
                time_frame=TF.ONE_MINUTE,
            )
        except Exception as e:
            print(f"Histada download error: {e}")
            return None

    def _process_data(
        self, symbol: str, year: int, month: Optional[int] = None, dfs: list = None
    ) -> None:
        """指定された年月のデータをダウンロードして処理する"""
        print(f"Downloading {symbol} for {year}" + (f"-{month}" if month else ""))

        # 設定ファイルからデータ保存パスを取得
        raw_data_path = self.config.get_storage_path("raw/forex")
        raw_data_path.mkdir(parents=True, exist_ok=True)

        # カレントディレクトリを一時的に変更してダウンロード
        original_dir = Path.cwd()
        try:
            # ダウンロード先ディレクトリに移動
            os.chdir(raw_data_path)
            self._download_data(symbol, year, month)

            current_year = datetime.datetime.now().year
            if year == current_year:
                # 今年のデータは月ごとのZIPファイル
                if month:
                    zip_file_path = Path(
                        f"DAT_ASCII_{symbol.upper().replace('/', '')}_M1_{year}{month:02d}.zip"
                    )
                else:
                    for m in range(1, 13):
                        zip_file_path = Path(
                            f"DAT_ASCII_{symbol.upper().replace('/', '')}_M1_{year}{m:02d}.zip"
                        )
                        if zip_file_path.exists():
                            shutil.unpack_archive(str(zip_file_path), ".")
                            print(f"Successfully unpacked archive to: {raw_data_path}")
                            zip_file_path.unlink()  # 使用後にZIPファイルを削除
                        else:
                            print(f"ZIP file not found: {zip_file_path}")
                    return
            else:
                # 過去のデータは年単位のZIPファイル
                zip_file_path = Path(
                    f"DAT_ASCII_{symbol.upper().replace('/', '')}_M1_{year}.zip"
                )

            if zip_file_path.exists():
                shutil.unpack_archive(str(zip_file_path), ".")
                print(f"Successfully unpacked archive to: {raw_data_path}")
                zip_file_path.unlink()  # 使用後にZIPファイルを削除
            else:
                print(f"ZIP file not found: {zip_file_path}")
                return

            if year == current_year:
                # 今年のデータは月ごとのファイル
                if month:
                    csv_file_path = Path(
                        f"DAT_ASCII_{symbol.upper().replace('/', '')}_M1_{year}{month:02d}.csv"
                    )
                    self._process_csv_file(csv_file_path, dfs)
                else:
                    for m in range(1, 13):
                        csv_file_path = Path(
                            f"DAT_ASCII_{symbol.upper().replace('/', '')}_M1_{year}{m:02d}.csv"
                        )
                        self._process_csv_file(csv_file_path, dfs)
            else:
                # 過去のデータは年単位のファイル
                csv_file_path = Path(
                    f"DAT_ASCII_{symbol.upper().replace('/', '')}_M1_{year}.csv"
                )
                self._process_csv_file(csv_file_path, dfs)

        except Exception as e:
            print(f"Error processing data: {e}")
        finally:
            # 元のディレクトリに戻る
            os.chdir(original_dir)

    def _process_csv_file(self, csv_file_path: Path, dfs: list) -> None:
        """CSVファイルを処理する"""
        try:
            if csv_file_path.exists():
                df = pd.read_csv(csv_file_path)
                dfs.append(df)
            else:
                print(f"CSV file not found: {csv_file_path}")
        except Exception as e:
            print(f"Error reading CSV file {csv_file_path}: {e}")

    def fetch_data(
        self, data_type: DataType, symbol: str, start_date: str, end_date: str, **kwargs
    ) -> Optional[pd.DataFrame]:
        """データを取得する"""
        try:
            if data_type == DataType.FOREX:
                start_year = int(start_date[:4])
                start_month = int(start_date[5:7])
                end_year = int(end_date[:4])
                end_month = int(end_date[5:7])

                dfs = []
                for year in range(start_year, end_year + 1):
                    start_month_for_year = start_month if year == start_year else 1
                    end_month_for_year = end_month if year == end_year else 12

                    if year == datetime.datetime.now().year:
                        for month in range(
                            start_month_for_year, end_month_for_year + 1
                        ):
                            self._process_data(symbol, year, month, dfs)
                    else:
                        self._process_data(symbol, year, None, dfs)

                if not dfs:
                    return None

                return pd.concat(dfs)
        except Exception as e:
            print(f"Histada error: {e}")
            return None
