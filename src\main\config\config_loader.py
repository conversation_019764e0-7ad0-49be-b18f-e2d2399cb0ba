from pathlib import Path
from typing import Any, Dict

import tomli
import yaml


class ConfigLoader:
    """設定ファイルを読み込むためのクラス"""

    def __init__(self, config_path: str = "config/market_data_config.yaml"):
        """
        Args:
            config_path (str): 設定ファイルのパス
        """
        self.project_root = self._get_project_root()
        self.package_root = self._get_package_root()
        self.config_path = self.project_root / "src" / "main" / config_path
        self.config = self._load_config()

    def _get_project_root(self) -> Path:
        """pyproject.tomlからプロジェクトルートを取得"""
        current_dir = Path.cwd()
        while current_dir != current_dir.parent:
            if (current_dir / "pyproject.toml").exists():
                return current_dir
            current_dir = current_dir.parent
        raise FileNotFoundError("pyproject.toml not found")

    def _get_package_root(self) -> Path:
        """pyproject.tomlからパッケージルートを取得"""
        with open(self.project_root / "pyproject.toml", "rb") as f:
            pyproject = tomli.load(f)

        # [tool.setuptools.packages.find]からパッケージルートを構築
        packages_config = (
            pyproject.get("tool", {})
            .get("setuptools", {})
            .get("packages", {})
            .get("find", {})
        )
        where = packages_config.get("where", ["src"])[0]  # デフォルトは"src"
        include = packages_config.get("include", ["main*"])[0].replace(
            "*", ""
        )  # "main*"から"main"を取得

        return Path(where) / include

    def _load_config(self) -> Dict[str, Any]:
        """設定ファイルを読み込む"""
        with open(self.config_path, "r", encoding="utf-8") as f:
            return yaml.safe_load(f)

    def get_storage_path(self, data_type: str) -> Path:
        """データ保存パスを取得"""
        storage_config = self.config["data_storage"]
        # package_rootを使用してベースパスを構築
        base_path = self.package_root / storage_config["base_path"]

        # data_typeが"raw/forex"のような形式の場合の処理
        parts = data_type.split("/")
        if len(parts) > 1 and parts[0] in storage_config["sub_paths"]:
            return base_path / parts[0] / parts[1]

        # サブパスが指定されていない場合はbase_pathの直下に保存
        return base_path / data_type

    def get_symbols(self, data_type: str) -> list:
        """指定されたデータタイプのシンボルリストを取得"""
        return self.config["market_data"][data_type]["symbols"]

    def get_default_source(self, data_type: str) -> str:
        """指定されたデータタイプのデフォルトのデータソースを取得"""
        return self.config["market_data"][data_type]["default_source"]

    def get_time_period(self, period_type: str = "default") -> Dict[str, str]:
        """期間設定を取得"""
        return self.config["time_periods"][period_type]
