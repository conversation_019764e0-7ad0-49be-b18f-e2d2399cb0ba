# 拡散モデルと逆強化学習を用いた自動売買システム構築手順
## 1. データ収集と準備

* **株価データの収集:**
    * 過去の株価データ（日足、時間足など）を収集する。
    * 複数の銘柄のデータを収集することで、モデルの汎化性能を高めることができる。
* **経済指標データの収集:**
    * GDP、金利、消費者物価指数など、株価に影響を与える可能性のある経済指標データを収集する。
    * データの時間軸を株価データと一致させる。
* **エキスパートの取引データの収集：**
    * 熟練トレーダーの過去の取引データ（売買履歴、取引戦略など）を収集する。
    * このデータは、逆強化学習におけるエキスパートの行動データとして使用する。
* **データのクレンジングと前処理:**
    * 欠損値や外れ値を処理し、データの品質を高める。
    * データを正規化または標準化し、モデルの学習を安定させる。
    * 時系列データをモデルに入力しやすい形式に変換する。
* **データの分割:**
    * データを学習用、検証用、テスト用に分割する。
    * 時系列データの特性を考慮し、時間的に連続したデータで分割する。

## 2. 拡散モデルの構築による未来データの生成

* **モデルの選択:**
    * 時系列データに対応した拡散モデル（例: 時系列拡散モデル）を選択する。
    * 必要に応じて、条件付き拡散モデルを選択する。
* **モデルの設計:**
    * モデルのアーキテクチャ（層の数、活性化関数など）を設計する。
    * ハイパーパラメータ（学習率、バッチサイズなど）を設定する。
    * 拡散生成対象は未来の価格とする。
    * 追加のコンテキストとして現在までの価格および経済指標データを追加する。
* **モデルの学習:**
    * 学習用データを用いて、拡散モデルを学習する。
    * 検証用データを用いて、モデルの性能を評価し、ハイパーパラメータを調整する。
* **モデルの評価:**
    * テスト用データを用いて、モデルの予測精度を評価する。
    * 必要に応じて、モデルを再学習または調整する。

## 3. 逆強化学習による報酬関数の学習

* **特徴量の設計：**
    * 株価データ、経済指標データ、拡散モデルで生成した未来の株価予測など、報酬関数に含める特徴量を設計する。
* **モデルの選択：**
    * 逆強化学習アルゴリズム（例：最大エントロピー逆強化学習、GAIL）を選択する。
* **エキスパートデータの準備：**
    * 過去データを用いて、エキスパートの行動データを準備する。
* **報酬関数の学習：**
    * エキスパートの取引データを用いて、報酬関数を学習する。
    * 学習した報酬関数は、強化学習エージェントの学習目標として使用する。
* **報酬関数の評価：**
    * 学習した報酬関数を用いて、エキスパートの行動を再現できるか評価する。
    * 必要に応じて、報酬関数の特徴量やパラメータを調整する。

## 4. 強化学習エージェントの構築と学習

* **環境の構築:**
    * 株価データ、経済指標データ、拡散モデルで生成した未来の株価予測、口座残高、保有株数などを含む環境を構築する。
    * データはGNNのグラフ構造に変換し、gymnasiumのSpaceと相互に変換出来るようにする。
    * 学習した報酬関数を環境に組み込む。
    * 環境は、強化学習フレームワーク（例: OpenAI Gym）を用いて実装する。
* **エージェントの設計:**
    * 強化学習アルゴリズム（例: DQN、PPO）を選択する。
    * エージェントのアーキテクチャ（層の数、活性化関数など）を設計する。
    * 報酬関数を設計する。
* **エージェントの学習:**
    * 環境と相互作用させながら、エージェントを学習する。
    * 学習過程をモニタリングし、必要に応じてエージェントのパラメータを調整する。
* **エージェントの評価:**
    * テスト環境でエージェントの取引パフォーマンスを評価する。
    * シャープレシオ、最大ドローダウンなど、適切な評価指標を用いる。

## 5. システムの統合とテスト

* **リアルタイムなデータ更新と予測を行うための仕組みの構築:**
    * 強化学習エージェントを実際の市場で取引できるようにするための仕組みを構築する。
* **バックテスト:**
    * 過去のデータを用いて、システムの取引パフォーマンスを評価する。
    * 様々な市場シナリオを想定し、システムのロバスト性を検証する。
* **フォワードテスト:**
    * 実際の市場データを用いて、システムの取引パフォーマンスを評価する。
    * リスク管理を行いながら、小規模な取引から開始する。

## 6. システムの改善と運用

* **パフォーマンスのモニタリング:**
    * システムの取引パフォーマンスを継続的にモニタリングする。
    * 市場の変化やモデルの劣化に応じて、システムのパラメータを調整する。
* **モデルの再学習:**
    * 定期的にモデルを再学習し、最新の市場データに適応させる。
    * 新たなデータソースや技術を取り入れ、モデルの性能を向上させる。
* **報酬関数の更新：**
    * 市場の変化やエキスパートの取引戦略の変化に応じて、報酬関数を定期的に更新する。
    * 新たなエキスパートの取引データを取り入れ、報酬関数の精度を向上させる。
* **リスク管理:**
    * 損失を最小限に抑えるために、リスク管理戦略を策定する。
    * 損切りルールやポジションサイズ管理などを設定する。

## アーキテクチャ
| 目的 | 技術 |
|---|---|
| 未来データ生成 | 拡散モデル |
| 拡散モデルライブラリ | TensorFlow Probability |
| 強化学習 | グラフ強化学習 |
| グラフ強化学習ライブラリ | TensorFlow GNN |
| 強化学習フレームワーク | SimpleDistributedRL |
| 強化学習環境 | gymnasium |
| 報酬設計 | 逆強化学習 |
| 逆強化学習ライブラリ | 未定 |
| 経済指標データ取得(ヒストリカル) | Pandas-Datareader |
| 経済指標データ取得(リアルタイム) | 未定 |
| 為替レート取得(リアルタイム) | GMOコイン |
| 為替レート取得(ヒストリカル) | histdata |

### グラフ構造をSRL内で扱うために
SRLのSpaceBaseクラスを継承するtf-gnnのSpaces.graphクラスのラッパークラスを作成する。

```python
import gymnasium as gym
from gymnasium.spaces import Space
from tfgnn import spaces  # tf-gnn の spaces モジュールをインポート
import tensorflow as tf

class TfgnnGraphWrapper(Space):
    def __init__(self, tfgnn_graph_space):
        # tf-gnn の GraphSpace を内部に保持
        self.tfgnn_graph_space = tfgnn_graph_space

        # Gymnasium Space の __init__ を呼び出す (shape, dtype は tf-gnn 側から取得)
        super().__init__(shape=tfgnn_graph_space.shape, dtype=tfgnn_graph_space.dtype)

        # tfgnn.GraphTensor の情報を保持
        self.node_sizes = tfgnn_graph_space.node_sizes
        self.adjacency_lists = tfgnn_graph_space.adjacency_lists
        self.node_features = tfgnn_graph_space.node_features

    def sample(self):
        # tf-gnn の GraphSpace の sample() を呼び出す
        return self.tfgnn_graph_space.sample()

    def contains(self, x):
        # tf-gnn の GraphSpace の contains() を呼び出す
        return self.tfgnn_graph_space.contains(x)
```

## 強化学習モデルの選択
| モデル | ベース | ポリシー | アルゴリズム | 観測値 | 行動 |
|---|---|---|---|---|---|
| モデルフリー | 価値ベース | オフポリシー | Go-Explore | 連続 | 離散 |

## 逆強化学習のアルゴリズムの選択
| アルゴリズム | 説明 | 特徴 | 問題点 | 改善点 | 参考 |
|---|---|---|---|---|---|
| 逆強化学習 |  |  |  |  |  |

## エキスパートデータの作成方法
* １時間足の高値・安値で売買する
* 高値が先に来た場合は売りから入る
* 安値が先に来た場合は買いから入る
* 次の時間足で続落・続騰する場合は決済せずにホールドする