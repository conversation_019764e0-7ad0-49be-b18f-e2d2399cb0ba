from gymnasium.spaces import Space, Box, Dict
import numpy as np
from typing import Any, Optional

class MarketDataSpace(Space[Dict]):
    def __init__(self):
        # 基本的なSpace設定
        super().__init__(shape=None, dtype=None)
        
        # データ保持用の内部Space定義
        self.observation_space = Dict({
            "raw_data": Box(low=-np.inf, high=np.inf, shape=(100, 10)),  # 生データ
            "processed_data": Box(low=-np.inf, high=np.inf, shape=(100, 5)),  # 前処理後
            "graph_data": Box(low=-np.inf, high=np.inf, shape=(100, 3))  # グラフ用
        })