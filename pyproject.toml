[build-system]
requires = ["setuptools>=64.0.0", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "FXTRADEAPP"
version = "0.0.0"
description = "Automated FX Trading System using Diffusion Models and Inverse Reinforcement Learning"
readme = "README.md"
requires-python = ">=3.8"
license = { text = "MIT" }
dependencies = [
    "gymnasium>=1.0.0",
    "matplotlib>=3.10.0",
    "numpy>=2.0.2",
    "pandas>=2.2.3",
    "tensorflow>=2.18.0",
    "tensorflow-gnn>=1.0.3",
    "tensorflow-probability>=0.25.0",
    "pytest>=8.3.5",
    "Pandas-Datareader>=0.10.0",
    "tomli==2.2.1",
]

[tool.setuptools.packages.find]
where = ["src"]
include = ["main*"]
namespaces = false

[tool.pytest.ini_options]
testpaths = ["tests"]
python_files = ["test_*.py"]

[tool.black]
line-length = 88
target-version = ['py38']

[tool.isort]
profile = "black"
multi_line_output = 3

[tool.mypy]
python_version = "3.8"
strict = true
ignore_missing_imports = true
