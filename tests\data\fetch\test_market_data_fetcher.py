from data.market_data_fetcher import MarketDataFetcher, DataType, DataSource
from datetime import datetime, timedelta

def main():
    # API キーの設定
    api_keys = {
        "fred": "your_fred_api_key",
        "boj": "your_boj_api_key"
    }

    # MarketDataFetcherのインスタンスを作成
    fetcher = MarketDataFetcher(api_keys)

    # 日付設定
    end_date = datetime.now().strftime("%Y-%m-%d")
    start_date = (datetime.now() - timedelta(days=30)).strftime("%Y-%m-%d")

    # 株価データの取得（トヨタ自動車）
    stock_data = fetcher.fetch_data(
        data_type=DataType.STOCK,
        symbol="7203.T",
        start_date=start_date,
        end_date=end_date,
        source=DataSource.YAHOO
    )
    print("Stock Data (Toyota):")
    print(stock_data.head())

    # 為替データの取得（USD/JPY）
    forex_data = fetcher.fetch_data(
        data_type=DataType.FOREX,
        symbol="USD/JPY",
        start_date=start_date,
        end_date=end_date,
        source=DataSource.INVESTPY
    )
    print("\nForex Data (USD/JPY):")
    print(forex_data.head())

    # 金利データの取得（日本の政策金利）
    interest_rate_data = fetcher.fetch_data(
        data_type=DataType.INTEREST_RATE,
        symbol="INTDSRJPM193N",  # FREDのシリーズID
        start_date=start_date,
        end_date=end_date,
        source=DataSource.FRED
    )
    print("\nInterest Rate Data:")
    print(interest_rate_data.head())

if __name__ == "__main__":
    main()