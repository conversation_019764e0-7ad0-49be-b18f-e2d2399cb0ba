from data.fetch.market_data_fetcher import DataType, MarketDataFetcher


def main():
    # フェッチャーの初期化（設定ファイルのパスを指定）
    fetcher = MarketDataFetcher(config_path="config/market_data_config.yaml")

    # 為替データの一括取得
    forex_data = fetcher.fetch_all_data(
        data_type=DataType.FOREX, period_type="historical"
    )

    # 取得したデータの確認（最初の10行）
    for symbol, data in forex_data.items():
        print(f"\n{symbol}のデータ:")
        print(data.head(10))


if __name__ == "__main__":
    main()
