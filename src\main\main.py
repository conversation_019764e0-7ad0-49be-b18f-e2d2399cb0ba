def main():
    # 環境とエージェントの初期化
    env = MarketEnvironment()
    agent = MarketAgent(env.observation_space, env.action_space)
    
    # 初期観測の取得
    observation, _ = env.reset()
    
    for _ in range(1000):
        # エージェントによる行動の決定
        action = agent.act(observation)
        
        # 環境の更新
        observation, reward, terminated, truncated, info = env.step(action)
        
        if terminated or truncated:
            observation, _ = env.reset()

if __name__ == "__main__":
    main()